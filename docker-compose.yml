version: '3.8'

services:
  # 主应用服务
  amp-proxy:
    build: .
    ports:
      - "8000:8000"
    environment:
      # 从 .env 文件加载环境变量
      - LOG_LEVEL=${LOG_LEVEL}
      - API_KEY=${API_KEY}
      - DATABASE_URL=**********************************************/ampproxy
      - UPSTASH_REDIS_REST_URL=http://redis:6379
      - UPSTASH_REDIS_REST_TOKEN=local-redis-token
      - SELF_HOSTED_GATEWAY_URL=${SELF_HOSTED_GATEWAY_URL}
      - PORTKEY_API_KEY=${PORTKEY_API_KEY}
      - PORTKEY_CUSTOM_HOST=${PORTKEY_CUSTOM_HOST}
      - PORTKEY_PROVIDER=${PORTKEY_PROVIDER}
      - HUMKT_API_KEY=${HUMKT_API_KEY}
      - STEEL_API_KEY=${STEEL_API_KEY}
      - MAIL_API_BASE_URL=${MAIL_API_BASE_URL}
      - MAIL_DOMAIN=${MAIL_DOMAIN}
    env_file:
      - .env
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    volumes:
      # 可选：挂载日志目录
      - ./logs:/app/logs
    networks:
      - amp-network

  # 本地 PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: ampproxy
      POSTGRES_USER: ampuser
      POSTGRES_PASSWORD: amppassword
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - amp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ampuser -d ampproxy"]
      interval: 30s
      timeout: 10s
      retries: 5

  # 本地 Redis
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - amp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

networks:
  amp-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
