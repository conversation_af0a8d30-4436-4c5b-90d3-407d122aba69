version: '3.8'

services:
  # 主应用服务
  amp-proxy:
    build: .
    ports:
      - "8000:8000"
    environment:
      # 从 .env 文件加载环境变量
      - LOG_LEVEL=${LOG_LEVEL}
      - API_KEY=${API_KEY}
      - DATABASE_URL=${DATABASE_URL}
      - UPSTASH_REDIS_REST_URL=${UPSTASH_REDIS_REST_URL}
      - UPSTASH_REDIS_REST_TOKEN=${UPSTASH_REDIS_REST_TOKEN}
      - SELF_HOSTED_GATEWAY_URL=${SELF_HOSTED_GATEWAY_URL}
      - PORTKEY_API_KEY=${PORTKEY_API_KEY}
      - PORTKEY_CUSTOM_HOST=${PORTKEY_CUSTOM_HOST}
      - PORTKEY_PROVIDER=${PORTKEY_PROVIDER}
      - HUMKT_API_KEY=${HUMKT_API_KEY}
      - STEEL_API_KEY=${STEEL_API_KEY}
      - MAIL_API_BASE_URL=${MAIL_API_BASE_URL}
      - MAIL_DOMAIN=${MAIL_DOMAIN}
    env_file:
      - .env
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    volumes:
      # 可选：挂载日志目录
      - ./logs:/app/logs
    networks:
      - amp-network

  # 可选：本地 PostgreSQL 数据库（如果不使用 Neon）
  # postgres:
  #   image: postgres:15-alpine
  #   environment:
  #     POSTGRES_DB: ampproxy
  #     POSTGRES_USER: ampuser
  #     POSTGRES_PASSWORD: amppassword
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   networks:
  #     - amp-network

  # 可选：本地 Redis（如果不使用 Upstash）
  # redis:
  #   image: redis:7-alpine
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   networks:
  #     - amp-network

networks:
  amp-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
