# AMP OpenAI API 代理服务部署指南

## 📋 概述

本项目是一个基于 Deno 和 Hono 框架的 AI API 代理服务，主要功能是将 Anthropic Claude API 转换为 OpenAI 兼容的 API 格式，并提供自动化的 API 密钥管理功能。

## 🚀 快速开始

### 1. 环境要求

- Docker 和 Docker Compose
- 至少 2GB 可用内存
- 至少 5GB 可用磁盘空间

### 2. 配置环境变量

编辑 `.env` 文件，配置以下必需的环境变量：

```bash
# API 认证密钥 (客户端访问本服务时使用)
API_KEY=your-secure-api-key-here

# Portkey API 网关配置 (必需)
SELF_HOSTED_GATEWAY_URL=https://api.portkey.ai
PORTKEY_API_KEY=your-portkey-api-key
PORTKEY_CUSTOM_HOST=api.anthropic.com
PORTKEY_PROVIDER=anthropic

# Steel SDK 配置 (用于自动化注册)
STEEL_API_KEY=ste-your-steel-api-key

# HUMKT API 配置 (用于自动购买账户)
HUMKT_API_KEY=your-humkt-api-key

# 邮箱服务配置 (您的邮箱服务)
MAIL_API_BASE_URL=https://mail.hiclover.me
MAIL_DOMAIN=mail.hiclover.me
```

### 3. 启动服务

```bash
# 给部署脚本执行权限
chmod +x deploy.sh

# 启动服务
./deploy.sh start
```

### 4. 验证部署

```bash
# 检查服务状态
./deploy.sh status

# 查看日志
./deploy.sh logs

# 健康检查
./deploy.sh health
```

## 🔧 详细配置说明

### 必需配置

#### 1. API_KEY
- **用途**: 客户端访问本服务的认证密钥
- **示例**: `amp-proxy-2024-secure-key`
- **注意**: 请使用强密码，客户端需要在 Authorization header 中使用

#### 2. Portkey 配置
- **PORTKEY_API_KEY**: Portkey 服务的 API 密钥
- **获取方式**: 在 [Portkey](https://portkey.ai) 注册并获取 API 密钥
- **用途**: 用于转发请求到 Anthropic API

#### 3. Steel SDK 配置
- **STEEL_API_KEY**: Steel 浏览器自动化服务的 API 密钥
- **获取方式**: 在 [Steel](https://steel.dev) 注册并获取 API 密钥
- **用途**: 用于自动化注册 AMP 账户

### 可选配置

#### 1. HUMKT API 配置
- **用途**: 自动购买账户服务
- **如果不配置**: 需要手动管理 API 密钥

#### 2. 邮箱服务配置
- **MAIL_API_BASE_URL**: 您的邮箱服务 API 地址
- **MAIL_DOMAIN**: 邮箱域名
- **用途**: 接收验证码邮件

## 🗄️ 数据库说明

项目使用本地 PostgreSQL 数据库，包含以下表：

- **api_keys**: 存储 API 密钥信息
- **api_requests**: 记录 API 请求日志

数据库会在首次启动时自动初始化，并插入示例数据。

## 📊 监控和管理

### 查看 API 密钥状态

```bash
curl -H "Authorization: Bearer your-api-key" http://localhost:8000/api-keys
```

### 查看服务日志

```bash
./deploy.sh logs
```

### 查看数据库统计

连接到 PostgreSQL 数据库：

```bash
docker exec -it amp-open-ai-postgres-1 psql -U ampuser -d ampproxy
```

查看统计信息：

```sql
-- 查看活跃 API 密钥统计
SELECT * FROM active_api_keys_stats;

-- 查看请求统计
SELECT * FROM api_request_stats;

-- 查看可用的 API 密钥
SELECT * FROM get_available_api_key();
```

## 🔄 常用操作

### 启动服务
```bash
./deploy.sh start
```

### 停止服务
```bash
./deploy.sh stop
```

### 重启服务
```bash
./deploy.sh restart
```

### 查看日志
```bash
./deploy.sh logs
```

### 清理资源
```bash
./deploy.sh cleanup
```

## 🐛 故障排除

### 1. 数据库连接失败
- 检查 PostgreSQL 容器是否正常运行
- 确认数据库连接字符串正确

### 2. Redis 连接失败
- 检查 Redis 容器是否正常运行
- 项目使用 Deno KV 作为本地存储替代方案

### 3. API 请求失败
- 检查 Portkey 配置是否正确
- 确认 API_KEY 设置正确
- 查看应用日志获取详细错误信息

### 4. 自动注册失败
- 检查 Steel API 密钥是否有效
- 确认邮箱服务配置正确
- 查看浏览器自动化日志

## 🔒 安全建议

1. **更改默认密码**: 修改 `.env` 文件中的所有默认密码
2. **使用 HTTPS**: 在生产环境中使用 HTTPS
3. **定期备份**: 定期备份数据库数据
4. **监控日志**: 定期检查应用日志，发现异常行为
5. **限制访问**: 使用防火墙限制对服务的访问

## 📞 支持

如果遇到问题，请：

1. 查看应用日志：`./deploy.sh logs`
2. 检查服务状态：`./deploy.sh status`
3. 执行健康检查：`./deploy.sh health`

## 🔄 更新服务

```bash
# 停止服务
./deploy.sh stop

# 拉取最新代码
git pull

# 重新构建并启动
./deploy.sh start
```
