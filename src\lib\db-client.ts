import { Client } from "postgres";
import { env } from "@/env-runtime.ts";

// 解析数据库 URL
function parseDatabaseUrl(url: string) {
  const parsed = new URL(url);
  return {
    hostname: parsed.hostname,
    port: parseInt(parsed.port) || 5432,
    database: parsed.pathname.slice(1), // 移除开头的 /
    username: parsed.username,
    password: parsed.password,
  };
}

// 创建数据库客户端
function createClient() {
  const dbConfig = parseDatabaseUrl(env.DATABASE_URL);
  
  return new Client({
    hostname: dbConfig.hostname,
    port: dbConfig.port,
    database: dbConfig.database,
    user: dbConfig.username,
    password: dbConfig.password,
    tls: {
      enabled: false, // 本地数据库不需要 TLS
    },
  });
}

// 数据库连接池管理
class DatabasePool {
  private client: Client | null = null;
  private isConnecting = false;

  async getClient(): Promise<Client> {
    if (this.client && !this.client.closed) {
      return this.client;
    }

    if (this.isConnecting) {
      // 等待连接完成
      while (this.isConnecting) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      if (this.client && !this.client.closed) {
        return this.client;
      }
    }

    this.isConnecting = true;
    try {
      this.client = createClient();
      await this.client.connect();
      console.log("数据库连接成功");
      return this.client;
    } catch (error) {
      console.error("数据库连接失败:", error);
      throw error;
    } finally {
      this.isConnecting = false;
    }
  }

  async query(sql: string, params: any[] = []): Promise<any[]> {
    const client = await this.getClient();
    try {
      const result = await client.queryArray(sql, params);
      return result.rows;
    } catch (error) {
      console.error("数据库查询失败:", error);
      throw error;
    }
  }

  async queryObject(sql: string, params: any[] = []): Promise<any[]> {
    const client = await this.getClient();
    try {
      const result = await client.queryObject(sql, params);
      return result.rows;
    } catch (error) {
      console.error("数据库查询失败:", error);
      throw error;
    }
  }

  async close() {
    if (this.client && !this.client.closed) {
      await this.client.end();
      this.client = null;
    }
  }
}

// 导出单例实例
export const db = new DatabasePool();

// 模板字符串 SQL 函数，兼容 Neon 的 API
export function sql(strings: TemplateStringsArray, ...values: any[]) {
  let query = strings[0];
  const params: any[] = [];
  
  for (let i = 0; i < values.length; i++) {
    const value = values[i];
    if (typeof value === 'object' && value !== null && 'raw' in value) {
      // 处理原始 SQL 片段
      query += value.raw + strings[i + 1];
    } else {
      // 处理参数
      params.push(value);
      query += `$${params.length}` + strings[i + 1];
    }
  }
  
  return {
    async then(resolve: (result: any[]) => void, reject?: (error: any) => void) {
      try {
        const result = await db.queryObject(query, params);
        resolve(result);
      } catch (error) {
        if (reject) {
          reject(error);
        } else {
          throw error;
        }
      }
    }
  };
}

// 原始 SQL 片段标记函数
sql.raw = (rawSql: string) => ({ raw: rawSql });

// 时间戳转换函数
sql.TO_TIMESTAMP = (timestamp: number) => sql.raw(`TO_TIMESTAMP(${timestamp})`);
