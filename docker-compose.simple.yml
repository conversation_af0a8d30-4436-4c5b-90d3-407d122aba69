version: '3.8'

services:
  # 本地 PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: ampproxy
      POSTGRES_USER: ampuser
      POSTGRES_PASSWORD: amppassword
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - amp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ampuser -d ampproxy"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 主应用服务
  amp-proxy:
    build: .
    ports:
      - "8000:8000"
    environment:
      - LOG_LEVEL=info
      - API_KEY=amp-proxy-demo-key-2024
      - DATABASE_URL=**********************************************/ampproxy
      - UPSTASH_REDIS_REST_URL=http://redis:6379
      - UPSTASH_REDIS_REST_TOKEN=local-redis-token
      - SELF_HOSTED_GATEWAY_URL=https://api.portkey.ai
      - PORTKEY_API_KEY=demo-portkey-key
      - PORTKEY_CUSTOM_HOST=api.anthropic.com
      - PORTKEY_PROVIDER=anthropic
      - HUMKT_API_KEY=demo-humkt-key
      - STEEL_API_KEY=demo-steel-key
      - MAIL_API_BASE_URL=https://mail.hiclover.me
      - MAIL_DOMAIN=mail.hiclover.me
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - amp-network

networks:
  amp-network:
    driver: bridge

volumes:
  postgres_data:
