import { addA<PERSON><PERSON><PERSON>, getTotalRemainingUsage } from "@/lib/db.ts";
import { HumktAPI } from "@/lib/humkt.ts";
import { env } from "@/env-runtime.ts";
import { registerAmp } from "../lib/amp-register.ts";

const api = new HumktAPI(env.HUMKT_API_KEY);

Deno.cron(
  "Check Remain and Register New Key",
  { minute: { every: 5 } }, // 改为每5分钟检查一次
  { backoffSchedule: [1000, 2000, 3000, 4000, 5000] },
  async () => {
    try {
      const totalRemainingUsage = await getTotalRemainingUsage();
      console.log("Total Remaining Usage:", totalRemainingUsage);

      if (totalRemainingUsage < 100) {
        console.log("剩余使用量不足，需要添加新的 API 密钥");

        // 检查 HUMKT API 密钥是否配置
        if (!env.HUMKT_API_KEY || env.HUMKT_API_KEY === "your-humkt-api-key") {
          console.log("HUMKT API 密钥未配置，跳过自动购买");
          return;
        }

        try {
          // 下单购买
          const order = await api.buyProduct(355, 1);
          console.log("订单ID:", order.data.id);

          // 查询订单
          const orderInfo = await api.getOrderWithPolling(order.data.id);
          console.log("订单详情:", orderInfo.data);

          for (const item of orderInfo.data) {
            const [email, password] = item.split("|");
            // 现在registerAmp会自动生成随机邮箱，所以email参数会被忽略
            const newKey = await registerAmp("", password);
            console.log("New Key:", newKey);
            if (newKey.apiKey && newKey.apiKey.startsWith("sgamp_user_")) {
              await addApiKey(newKey.apiKey);
            } else {
              console.error("获取的密钥无效:", newKey);
            }
          }
        } catch (purchaseError) {
          console.error("自动购买失败:", purchaseError.message);
          console.log("请手动添加 API 密钥或检查 HUMKT API 配置");
        }
      }
    } catch (error) {
      console.error("定时任务执行失败:", error.message);
    }
  }
);
