import { env } from "@/env-runtime.ts";

// 邮件数据接口
interface MailData {
  to: string;
  from: string;
  subject: string;
  text: string;
  html: string;
  date: string;
  attachments: any[];
  raw: string;
}

// API 响应接口
interface MailListResponse {
  mails: MailData[];
}

interface MailResponse {
  mail: MailData;
}

interface ErrorResponse {
  error: string;
}

/**
 * 邮箱服务 API 客户端
 * 用于与 MeteorMail API 交互
 */
export class MailService {
  private baseUrl: string;
  private domain: string;

  constructor() {
    this.baseUrl = env.MAIL_API_BASE_URL;
    this.domain = env.MAIL_DOMAIN;
  }

  /**
   * 生成随机邮箱地址
   * @param prefix 可选的前缀，如果不提供则生成随机前缀
   * @returns 完整的邮箱地址
   */
  generateRandomEmail(prefix?: string): string {
    if (!prefix) {
      // 生成随机前缀：8位字母数字组合
      const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
      prefix = '';
      for (let i = 0; i < 8; i++) {
        prefix += chars.charAt(Math.floor(Math.random() * chars.length));
      }
    }
    return `${prefix}@${this.domain}`;
  }

  /**
   * 获取指定邮箱的邮件列表
   * @param mailboxAddr 邮箱地址
   * @returns 邮件列表
   */
  async getMailList(mailboxAddr: string): Promise<MailData[]> {
    const encodedMailbox = encodeURIComponent(mailboxAddr);
    const url = `${this.baseUrl}/api/mails/${encodedMailbox}`;

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: MailListResponse = await response.json();
      return data.mails || [];
    } catch (error) {
      console.error('获取邮件列表失败:', error);
      return [];
    }
  }

  /**
   * 获取指定邮箱中特定索引的邮件
   * @param mailboxAddr 邮箱地址
   * @param index 邮件索引
   * @returns 邮件数据或null
   */
  async getMail(mailboxAddr: string, index: number): Promise<MailData | null> {
    const encodedMailbox = encodeURIComponent(mailboxAddr);
    const url = `${this.baseUrl}/api/mails/${encodedMailbox}/${index}`;

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          return null; // 邮件不存在
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: MailResponse = await response.json();
      return data.mail;
    } catch (error) {
      console.error('获取邮件失败:', error);
      return null;
    }
  }

  /**
   * 删除指定邮箱中特定索引的邮件
   * @param mailboxAddr 邮箱地址
   * @param index 邮件索引
   * @returns 是否删除成功
   */
  async deleteMail(mailboxAddr: string, index: number): Promise<boolean> {
    const encodedMailbox = encodeURIComponent(mailboxAddr);
    const url = `${this.baseUrl}/api/mails/${encodedMailbox}/${index}`;

    try {
      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.success === true;
    } catch (error) {
      console.error('删除邮件失败:', error);
      return false;
    }
  }

  /**
   * 等待接收包含验证码的邮件
   * @param mailboxAddr 邮箱地址
   * @param timeout 超时时间（毫秒），默认60秒
   * @param interval 检查间隔（毫秒），默认3秒
   * @returns 验证码字符串或null
   */
  async waitForVerificationCode(
    mailboxAddr: string,
    timeout: number = 60000,
    interval: number = 3000
  ): Promise<string | null> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        const mails = await this.getMailList(mailboxAddr);
        
        // 查找最新的邮件中的验证码
        for (const mail of mails) {
          const code = this.extractVerificationCode(mail);
          if (code) {
            console.log(`找到验证码: ${code}`);
            return code;
          }
        }
        
        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, interval));
      } catch (error) {
        console.error('检查邮件时出错:', error);
        await new Promise(resolve => setTimeout(resolve, interval));
      }
    }
    
    console.log('等待验证码超时');
    return null;
  }

  /**
   * 从邮件内容中提取验证码
   * @param mail 邮件数据
   * @returns 验证码字符串或null
   */
  private extractVerificationCode(mail: MailData): string | null {
    // 常见的验证码模式
    const patterns = [
      /verification code[:\s]*([0-9]{4,8})/i,
      /验证码[：:\s]*([0-9]{4,8})/i,
      /code[:\s]*([0-9]{4,8})/i,
      /\b([0-9]{6})\b/, // 6位数字
      /\b([0-9]{4})\b/, // 4位数字
    ];

    // 在邮件文本和HTML中搜索验证码
    const searchText = `${mail.text} ${mail.html} ${mail.subject}`;
    
    for (const pattern of patterns) {
      const match = searchText.match(pattern);
      if (match && match[1]) {
        return match[1];
      }
    }
    
    return null;
  }

  /**
   * 清空指定邮箱的所有邮件
   * @param mailboxAddr 邮箱地址
   * @returns 清空的邮件数量
   */
  async clearMailbox(mailboxAddr: string): Promise<number> {
    try {
      const mails = await this.getMailList(mailboxAddr);
      let deletedCount = 0;
      
      // 从后往前删除，避免索引变化问题
      for (let i = mails.length - 1; i >= 0; i--) {
        const success = await this.deleteMail(mailboxAddr, i);
        if (success) {
          deletedCount++;
        }
      }
      
      return deletedCount;
    } catch (error) {
      console.error('清空邮箱失败:', error);
      return 0;
    }
  }
}

// 导出单例实例
export const mailService = new MailService();
