-- AMP OpenAI API 代理服务数据库初始化脚本
-- 创建必要的表和初始数据

-- 创建 API 密钥表
CREATE TABLE IF NOT EXISTS api_keys (
    id SERIAL PRIMARY KEY,
    key TEXT NOT NULL UNIQUE,
    usage INT NOT NULL DEFAULT 0,
    usage_limit INT NOT NULL DEFAULT 1000,
    active BOOLEAN NOT NULL DEFAULT true,
    disabled_at TIMESTAMP,
    disabled_reason TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建 API 请求记录表
CREATE TABLE IF NOT EXISTS api_requests (
    id SERIAL PRIMARY KEY,
    api_key TEXT NOT NULL,
    success BOOLEAN NOT NULL,
    error_type TEXT,
    error_message TEXT,
    request_time TIMESTAMP,
    response_time TIMESTAMP,
    request_id TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_api_keys_active ON api_keys(active);
CREATE INDEX IF NOT EXISTS idx_api_keys_usage ON api_keys(usage);
CREATE INDEX IF NOT EXISTS idx_api_keys_created_at ON api_keys(created_at);
CREATE INDEX IF NOT EXISTS idx_api_requests_api_key ON api_requests(api_key);
CREATE INDEX IF NOT EXISTS idx_api_requests_success ON api_requests(success);
CREATE INDEX IF NOT EXISTS idx_api_requests_created_at ON api_requests(created_at);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为 api_keys 表创建更新时间触发器
DROP TRIGGER IF EXISTS update_api_keys_updated_at ON api_keys;
CREATE TRIGGER update_api_keys_updated_at
    BEFORE UPDATE ON api_keys
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 插入示例 API 密钥（仅在表为空时）
INSERT INTO api_keys (key, usage, usage_limit, active, disabled_at, disabled_reason, created_at)
SELECT 'sgamp_user_demo_001', 0, 1000, true, null, null, CURRENT_TIMESTAMP
WHERE NOT EXISTS (SELECT 1 FROM api_keys);

INSERT INTO api_keys (key, usage, usage_limit, active, disabled_at, disabled_reason, created_at)
SELECT 'sgamp_user_demo_002', 0, 1000, true, null, null, CURRENT_TIMESTAMP
WHERE NOT EXISTS (SELECT 1 FROM api_keys WHERE key = 'sgamp_user_demo_002');

-- 创建视图：活跃的 API 密钥统计
CREATE OR REPLACE VIEW active_api_keys_stats AS
SELECT 
    COUNT(*) as total_active_keys,
    SUM(usage_limit - usage) as total_remaining_usage,
    AVG(usage::float / usage_limit::float * 100) as avg_usage_percentage,
    MIN(usage_limit - usage) as min_remaining_usage,
    MAX(usage_limit - usage) as max_remaining_usage
FROM api_keys 
WHERE active = true;

-- 创建视图：API 请求统计
CREATE OR REPLACE VIEW api_request_stats AS
SELECT 
    DATE(created_at) as request_date,
    COUNT(*) as total_requests,
    COUNT(CASE WHEN success = true THEN 1 END) as successful_requests,
    COUNT(CASE WHEN success = false THEN 1 END) as failed_requests,
    ROUND(
        COUNT(CASE WHEN success = true THEN 1 END)::float / COUNT(*)::float * 100, 
        2
    ) as success_rate_percentage
FROM api_requests 
GROUP BY DATE(created_at)
ORDER BY request_date DESC;

-- 创建函数：获取可用的 API 密钥
CREATE OR REPLACE FUNCTION get_available_api_key()
RETURNS TABLE(
    id INT,
    key TEXT,
    usage INT,
    usage_limit INT,
    remaining_usage INT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ak.id,
        ak.key,
        ak.usage,
        ak.usage_limit,
        (ak.usage_limit - ak.usage) as remaining_usage
    FROM api_keys ak
    WHERE ak.active = true 
      AND ak.usage < ak.usage_limit
    ORDER BY ak.usage ASC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- 创建函数：记录 API 请求
CREATE OR REPLACE FUNCTION record_api_request(
    p_api_key TEXT,
    p_success BOOLEAN,
    p_error_type TEXT DEFAULT NULL,
    p_error_message TEXT DEFAULT NULL,
    p_request_time TIMESTAMP DEFAULT NULL,
    p_response_time TIMESTAMP DEFAULT NULL,
    p_request_id TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO api_requests (
        api_key, 
        success, 
        error_type, 
        error_message, 
        request_time, 
        response_time, 
        request_id
    ) VALUES (
        p_api_key,
        p_success,
        p_error_type,
        p_error_message,
        p_request_time,
        p_response_time,
        p_request_id
    );
END;
$$ LANGUAGE plpgsql;

-- 创建函数：更新 API 密钥使用量
CREATE OR REPLACE FUNCTION update_api_key_usage(
    p_key TEXT,
    p_increment INT DEFAULT 1
)
RETURNS VOID AS $$
BEGIN
    UPDATE api_keys 
    SET 
        usage = usage + p_increment,
        active = CASE 
            WHEN usage + p_increment >= usage_limit THEN false 
            ELSE active 
        END,
        disabled_at = CASE 
            WHEN usage + p_increment >= usage_limit THEN CURRENT_TIMESTAMP 
            ELSE disabled_at 
        END,
        disabled_reason = CASE 
            WHEN usage + p_increment >= usage_limit THEN 'usage_limit_exceeded' 
            ELSE disabled_reason 
        END
    WHERE key = p_key;
END;
$$ LANGUAGE plpgsql;

-- 输出初始化完成信息
DO $$
BEGIN
    RAISE NOTICE 'AMP OpenAI API 代理服务数据库初始化完成！';
    RAISE NOTICE '已创建表：api_keys, api_requests';
    RAISE NOTICE '已创建视图：active_api_keys_stats, api_request_stats';
    RAISE NOTICE '已创建函数：get_available_api_key, record_api_request, update_api_key_usage';
    RAISE NOTICE '已插入示例 API 密钥';
END $$;
