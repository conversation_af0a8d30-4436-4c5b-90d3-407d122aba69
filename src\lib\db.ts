import { sql, db } from "./db-client.ts";

export interface ApiKey {
  key: string; // API密钥值
  usage: number; // 使用次数
  usageLimit: number; // 使用次数限制
  active: boolean; // 是否激活
  disabledAt: number; // 禁用时间戳
  disabledReason: string; // 禁用原因
  createdAt: number; // 创建时间戳
}

export interface ApiRequestResult {
  apiKey: string; // 使用的API密钥
  success: boolean; // 请求是否成功
  errorType?: string; // 错误类型
  errorMessage?: string; // 错误信息
  timestamp: number; // 请求时间戳
  responseTime?: number; // 响应时间(毫秒)
  requestId?: string; // 请求ID
}

export enum ApiKeyDisabledReason {
  USAGE_LIMIT_EXCEEDED = "usage_limit_exceeded", // 超过使用次数限制
  AUTHENTICATION_ERROR = "authentication_error", // 认证错误
  MANUALLY_DISABLED = "manually_disabled", // 手动禁用
  EXPIRED = "expired", // 过期
  SECURITY_VIOLATION = "security_violation", // 安全违规
}

// 数据库客户端已在 db-client.ts 中初始化

// 数据库初始化函数
async function initializeDatabase() {
  let retries = 0;
  const maxRetries = 10;
  const retryDelay = 5000; // 5秒

  while (retries < maxRetries) {
    try {
      console.log(`尝试连接数据库... (${retries + 1}/${maxRetries})`);

      // Create the tables if they don't exist
      await sql`
        CREATE TABLE IF NOT EXISTS api_keys (
          id SERIAL PRIMARY KEY,
          key TEXT NOT NULL,
          usage INT NOT NULL,
          usage_limit INT NOT NULL,
          active BOOLEAN NOT NULL,
          disabled_at TIMESTAMP,
          disabled_reason TEXT,
          created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
        );
      `;

      await sql`
        CREATE TABLE IF NOT EXISTS api_requests (
          id SERIAL PRIMARY KEY,
          api_key TEXT NOT NULL,
          success BOOLEAN NOT NULL,
          error_type TEXT,
          error_message TEXT,
          request_time TIMESTAMP,
          response_time TIMESTAMP,
          request_id TEXT
        );
      `;

      // Check if the table is empty
      const { count } = await sql`SELECT COUNT(*)::INT as count FROM api_keys`.then(
        (rows: any[]) => rows[0]
      );

      if (count === 0) {
        // The table is empty, insert the api_keys records
        await sql`
          INSERT INTO api_keys (key, usage, usage_limit, active, disabled_at, disabled_reason, created_at) VALUES
            ('sgamp_user_demo_001', 0, 1000, true, null, null, CURRENT_TIMESTAMP),
            ('sgamp_user_demo_002', 0, 1000, true, null, null, CURRENT_TIMESTAMP)
        `;
        console.log("已插入示例 API 密钥");
      }

      console.log("数据库初始化成功");
      return;
    } catch (error) {
      console.error(`数据库连接失败 (${retries + 1}/${maxRetries}):`, error.message);
      retries++;

      if (retries >= maxRetries) {
        console.error("数据库连接失败，已达到最大重试次数");
        throw error;
      }

      console.log(`等待 ${retryDelay / 1000} 秒后重试...`);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }
}

// 延迟初始化数据库
setTimeout(async () => {
  try {
    await initializeDatabase();
  } catch (error) {
    console.error("数据库初始化失败:", error);
    // 不要退出进程，让应用继续运行，稍后可能会成功连接
  }
}, 2000); // 延迟2秒启动

export const getApiKeys = async () => {
  try {
    const apiKeys = await sql`SELECT * FROM api_keys`;
    return apiKeys as ApiKey[];
  } catch (error) {
    console.error("获取 API 密钥列表失败:", error);
    return [];
  }
};

export const getApiKey = async (key: string) => {
  try {
    const apiKey = await sql`SELECT * FROM api_keys WHERE key = ${key}`;
    if (apiKey.length > 0) {
      return apiKey[0] as ApiKey;
    }
    return null;
  } catch (error) {
    console.error("获取 API 密钥失败:", error);
    return null;
  }
};

export const addApiKey = async (key: string) => {
  try {
    await sql`INSERT INTO api_keys (key, usage, usage_limit, active, disabled_at, disabled_reason, created_at) VALUES (${key}, 0, 1000, true, null, null, CURRENT_TIMESTAMP)`;
    console.log(`成功添加 API 密钥: ${key}`);
  } catch (error) {
    console.error("添加 API 密钥失败:", error);
    throw error;
  }
};

export const getAvailableApiKey = async () => {
  try {
    const apiKey =
      await sql`SELECT * FROM api_keys WHERE active = true AND usage < usage_limit ORDER BY usage ASC LIMIT 1`;
    if (apiKey.length > 0) {
      return apiKey[0] as ApiKey;
    }
    return null;
  } catch (error) {
    console.error("获取可用 API 密钥失败:", error);
    return null;
  }
};

export const getTotalRemainingUsage = async () => {
  try {
    const result = await sql`
      SELECT SUM(usage_limit - usage) as total_remaining_usage
      FROM api_keys
      WHERE active = true
    `;
    if (result.length > 0 && result[0].total_remaining_usage !== null) {
      return Number(result[0].total_remaining_usage);
    }
    return 0;
  } catch (error) {
    console.error("获取剩余使用量失败:", error);
    return 0;
  }
};

export const updateApiKeyStatus = async (
  key: string,
  disabledAt?: number | null,
  disabledReason?: string | null
) => {
  try {
    if (
      disabledAt !== undefined &&
      disabledAt !== null &&
      disabledReason !== undefined &&
      disabledReason !== null
    ) {
      // Manual disable
      await sql`
        UPDATE api_keys
        SET active = false, disabled_at = ${sql.TO_TIMESTAMP(disabledAt / 1000)}, disabled_reason = ${disabledReason}
        WHERE key = ${key}
      `;
    } else {
      const reasonExceeded = ApiKeyDisabledReason.USAGE_LIMIT_EXCEEDED;
      await sql`
        UPDATE api_keys
        SET
          usage = usage + 1,
          active = CASE
                     WHEN usage + 1 >= usage_limit THEN false
                     ELSE active
                   END,
          disabled_at = CASE
                          WHEN usage + 1 >= usage_limit THEN CURRENT_TIMESTAMP
                          ELSE disabled_at
                        END,
          disabled_reason = CASE
                              WHEN usage + 1 >= usage_limit THEN ${reasonExceeded}
                              ELSE disabled_reason
                            END
        WHERE key = ${key} AND active = true
      `;
    }
  } catch (error) {
    console.error("更新 API 密钥状态失败:", error);
    throw error;
  }
};

export const recordApiRequest = async (
  apiKey: string,
  success: boolean,
  errorType: string | null,
  errorMessage: string | null,
  requestTime: number | null,
  responseTime: number | null,
  requestId: string | null
) => {
  try {
    await sql`INSERT INTO api_requests (api_key, success, error_type, error_message, request_time, response_time, request_id) VALUES (${apiKey}, ${success}, ${errorType}, ${errorMessage}, ${requestTime ? sql.TO_TIMESTAMP(requestTime / 1000) : null}, ${responseTime ? sql.TO_TIMESTAMP(responseTime / 1000) : null}, ${requestId})`;
  } catch (error) {
    console.error("记录 API 请求失败:", error);
    // 不抛出错误，避免影响主要业务流程
  }
};
