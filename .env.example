# ===========================================
# AMP OpenAI API 代理服务 - 环境变量配置
# ===========================================

# 日志级别 (debug, info, warn, error)
LOG_LEVEL=info

# API 认证密钥 (用于客户端访问本服务的认证)
# 客户端需要在 Authorization header 中使用: Bearer YOUR_API_KEY
API_KEY=your-secure-api-key-here

# ===========================================
# 数据库配置 (PostgreSQL)
# ===========================================
# Neon Database 连接字符串
# 格式: postgresql://username:password@host:port/database?sslmode=require
DATABASE_URL=postgresql://username:<EMAIL>/neondb?sslmode=require

# ===========================================
# Redis 配置 (Upstash Redis)
# ===========================================
# Upstash Redis REST API URL
UPSTASH_REDIS_REST_URL=https://xxx.upstash.io

# Upstash Redis REST API Token
UPSTASH_REDIS_REST_TOKEN=your-upstash-redis-token

# ===========================================
# Portkey API 网关配置
# ===========================================
# 自托管网关 URL (用于转发请求到 Anthropic)
SELF_HOSTED_GATEWAY_URL=https://api.portkey.ai

# Portkey API 密钥
PORTKEY_API_KEY=your-portkey-api-key

# Portkey 自定义主机
PORTKEY_CUSTOM_HOST=api.anthropic.com

# Portkey 提供商
PORTKEY_PROVIDER=anthropic

# ===========================================
# HUMKT API 配置 (自动购买服务)
# ===========================================
# HUMKT API 密钥 (用于自动购买账户)
HUMKT_API_KEY=your-humkt-api-key

# ===========================================
# Steel SDK 配置 (浏览器自动化)
# ===========================================
# Steel API 密钥 (用于自动化注册)
STEEL_API_KEY=ste-your-steel-api-key

# ===========================================
# 邮箱服务配置 (用于接收验证码)
# ===========================================
# 邮箱服务 API 基础 URL
MAIL_API_BASE_URL=https://mail.hiclover.me

# 邮箱域名 (用于生成随机邮箱)
MAIL_DOMAIN=mail.hiclover.me
