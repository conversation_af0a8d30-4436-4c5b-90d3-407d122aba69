# AMP OpenAI API 代理服务 - 配置总结

## 🎉 部署成功！

您的 AMP OpenAI API 代理服务已经成功部署并运行在 `http://localhost:8000`

## 📋 当前配置状态

### ✅ 已完成配置
- **数据库**: 本地 PostgreSQL (端口 5432)
- **应用服务**: 运行在端口 8000
- **认证**: Bearer token 认证已启用
- **API 密钥管理**: 数据库中已有示例密钥
- **邮箱服务**: 已集成您的邮箱 API (https://mail.hiclover.me)

### ⚠️ 需要配置的服务

为了完整使用所有功能，您还需要配置以下服务：

#### 1. Portkey API 网关 (必需)
```bash
PORTKEY_API_KEY=your-actual-portkey-api-key
```
- **获取地址**: https://portkey.ai
- **用途**: 转发请求到 Anthropic Claude API
- **当前状态**: 使用演示密钥，需要替换为真实密钥

#### 2. Steel SDK (可选 - 用于自动注册)
```bash
STEEL_API_KEY=ste-your-actual-steel-api-key
```
- **获取地址**: https://steel.dev
- **用途**: 浏览器自动化，用于自动注册 AMP 账户
- **当前状态**: 使用演示密钥

#### 3. HUMKT API (可选 - 用于自动购买)
```bash
HUMKT_API_KEY=your-actual-humkt-api-key
```
- **用途**: 自动购买账户服务
- **当前状态**: 使用演示密钥

## 🚀 使用方法

### 基本 API 调用

```bash
# 健康检查
curl -H "Authorization: Bearer amp-proxy-demo-key-2024" \
     http://localhost:8000/

# 查看 API 密钥状态
curl -H "Authorization: Bearer amp-proxy-demo-key-2024" \
     http://localhost:8000/api-keys
```

### OpenAI 兼容的 API 调用

```bash
# 非流式请求
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Authorization: Bearer amp-proxy-demo-key-2024" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-sonnet-20240229",
    "messages": [
      {"role": "user", "content": "Hello, how are you?"}
    ],
    "max_tokens": 100
  }'

# 流式请求
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Authorization: Bearer amp-proxy-demo-key-2024" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-sonnet-20240229",
    "messages": [
      {"role": "user", "content": "Hello, how are you?"}
    ],
    "max_tokens": 100,
    "stream": true
  }'
```

## 🔧 配置真实 API 密钥

### 方法 1: 修改环境变量文件

编辑 `.env` 文件：

```bash
# 必需配置
PORTKEY_API_KEY=pk-your-actual-portkey-key

# 可选配置
STEEL_API_KEY=ste-your-actual-steel-key
HUMKT_API_KEY=your-actual-humkt-key

# 自定义认证密钥
API_KEY=your-custom-secure-api-key
```

### 方法 2: 修改 docker-compose.simple.yml

直接在 docker-compose.simple.yml 文件中修改环境变量。

### 重启服务应用配置

```bash
docker-compose -f docker-compose.simple.yml down
docker-compose -f docker-compose.simple.yml up -d
```

## 📊 监控和管理

### 查看服务状态
```bash
docker-compose -f docker-compose.simple.yml ps
```

### 查看日志
```bash
docker-compose -f docker-compose.simple.yml logs -f amp-proxy
```

### 连接数据库
```bash
docker exec -it amp-open-ai-postgres-1 psql -U ampuser -d ampproxy
```

### 数据库查询示例
```sql
-- 查看所有 API 密钥
SELECT * FROM api_keys;

-- 查看活跃的 API 密钥
SELECT * FROM api_keys WHERE active = true;

-- 查看 API 请求统计
SELECT 
  DATE(created_at) as date,
  COUNT(*) as total_requests,
  COUNT(CASE WHEN success = true THEN 1 END) as successful_requests
FROM api_requests 
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

## 🔒 安全建议

1. **更改默认 API 密钥**
   ```bash
   API_KEY=your-very-secure-random-key-here
   ```

2. **使用 HTTPS** (生产环境)
   - 配置反向代理 (Nginx/Caddy)
   - 申请 SSL 证书

3. **限制访问**
   - 配置防火墙规则
   - 使用 VPN 或内网访问

4. **定期备份数据库**
   ```bash
   docker exec amp-open-ai-postgres-1 pg_dump -U ampuser ampproxy > backup.sql
   ```

## 🎯 下一步

1. **配置 Portkey API 密钥** (必需，用于实际 API 调用)
2. **测试 API 功能** (使用真实的 Anthropic API 密钥)
3. **配置自动化功能** (Steel SDK 和 HUMKT API)
4. **设置监控和告警**
5. **配置生产环境部署**

## 🆘 故障排除

### 常见问题

1. **API 调用失败**
   - 检查 Portkey API 密钥是否正确
   - 确认 Anthropic API 密钥是否有效

2. **数据库连接失败**
   - 检查 PostgreSQL 容器是否运行
   - 查看数据库日志

3. **自动注册失败**
   - 检查 Steel API 密钥
   - 确认邮箱服务配置正确

### 获取帮助

- 查看应用日志: `docker-compose -f docker-compose.simple.yml logs amp-proxy`
- 查看数据库日志: `docker-compose -f docker-compose.simple.yml logs postgres`
- 检查服务状态: `docker-compose -f docker-compose.simple.yml ps`

---

🎉 **恭喜！您的 AMP OpenAI API 代理服务已成功部署！**
