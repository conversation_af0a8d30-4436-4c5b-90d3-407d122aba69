import { env } from "@/env-runtime.ts";

/**
 * 本地 Redis 客户端适配器
 * 兼容 Upstash Redis 的 API 接口
 */
class LocalRedisClient {
  private baseUrl: string;

  constructor() {
    // 从环境变量获取 Redis 连接信息
    this.baseUrl = env.UPSTASH_REDIS_REST_URL || "http://redis:6379";
  }

  /**
   * 设置键值对
   */
  async set(key: string, value: any): Promise<string> {
    try {
      // 对于本地 Redis，我们使用内存存储作为简单实现
      // 在生产环境中，您可能需要使用真正的 Redis 客户端
      const serializedValue = JSON.stringify(value);
      
      // 这里使用 Deno KV 作为本地存储替代方案
      const kv = await Deno.openKv();
      await kv.set([key], serializedValue);
      kv.close();
      
      return "OK";
    } catch (error) {
      console.error("Redis set error:", error);
      throw error;
    }
  }

  /**
   * 获取键值
   */
  async get(key: string): Promise<any> {
    try {
      const kv = await Deno.openKv();
      const result = await kv.get([key]);
      kv.close();
      
      if (result.value) {
        return JSON.parse(result.value as string);
      }
      return null;
    } catch (error) {
      console.error("Redis get error:", error);
      return null;
    }
  }

  /**
   * 删除键
   */
  async del(key: string): Promise<number> {
    try {
      const kv = await Deno.openKv();
      await kv.delete([key]);
      kv.close();
      return 1;
    } catch (error) {
      console.error("Redis del error:", error);
      return 0;
    }
  }

  /**
   * 检查键是否存在
   */
  async exists(key: string): Promise<number> {
    try {
      const kv = await Deno.openKv();
      const result = await kv.get([key]);
      kv.close();
      return result.value ? 1 : 0;
    } catch (error) {
      console.error("Redis exists error:", error);
      return 0;
    }
  }

  /**
   * 设置键的过期时间
   */
  async expire(key: string, seconds: number): Promise<number> {
    try {
      // Deno KV 支持 TTL
      const kv = await Deno.openKv();
      const currentValue = await kv.get([key]);
      
      if (currentValue.value) {
        await kv.set([key], currentValue.value, { expireIn: seconds * 1000 });
        kv.close();
        return 1;
      }
      
      kv.close();
      return 0;
    } catch (error) {
      console.error("Redis expire error:", error);
      return 0;
    }
  }

  /**
   * 递增数值
   */
  async incr(key: string): Promise<number> {
    try {
      const kv = await Deno.openKv();
      const result = await kv.get([key]);
      
      let newValue = 1;
      if (result.value) {
        const currentValue = JSON.parse(result.value as string);
        newValue = (typeof currentValue === 'number' ? currentValue : 0) + 1;
      }
      
      await kv.set([key], JSON.stringify(newValue));
      kv.close();
      
      return newValue;
    } catch (error) {
      console.error("Redis incr error:", error);
      throw error;
    }
  }

  /**
   * 递减数值
   */
  async decr(key: string): Promise<number> {
    try {
      const kv = await Deno.openKv();
      const result = await kv.get([key]);
      
      let newValue = -1;
      if (result.value) {
        const currentValue = JSON.parse(result.value as string);
        newValue = (typeof currentValue === 'number' ? currentValue : 0) - 1;
      }
      
      await kv.set([key], JSON.stringify(newValue));
      kv.close();
      
      return newValue;
    } catch (error) {
      console.error("Redis decr error:", error);
      throw error;
    }
  }

  /**
   * 获取所有匹配模式的键
   */
  async keys(pattern: string): Promise<string[]> {
    try {
      const kv = await Deno.openKv();
      const keys: string[] = [];
      
      // Deno KV 的简单实现，实际使用中可能需要更复杂的模式匹配
      for await (const entry of kv.list({ prefix: [] })) {
        const key = entry.key[0] as string;
        if (this.matchPattern(key, pattern)) {
          keys.push(key);
        }
      }
      
      kv.close();
      return keys;
    } catch (error) {
      console.error("Redis keys error:", error);
      return [];
    }
  }

  /**
   * 简单的模式匹配实现
   */
  private matchPattern(key: string, pattern: string): boolean {
    if (pattern === "*") return true;
    if (pattern.includes("*")) {
      const regex = new RegExp(pattern.replace(/\*/g, ".*"));
      return regex.test(key);
    }
    return key === pattern;
  }

  /**
   * 清空所有数据
   */
  async flushall(): Promise<string> {
    try {
      const kv = await Deno.openKv();
      
      // 删除所有键
      for await (const entry of kv.list({ prefix: [] })) {
        await kv.delete(entry.key);
      }
      
      kv.close();
      return "OK";
    } catch (error) {
      console.error("Redis flushall error:", error);
      throw error;
    }
  }
}

export const REDIS_KEYS = {
  LAST_USED_API_KEY_INDEX: "last_used_api_key_index",
  API_KEYS: "api_keys",
} as const;

// 根据环境变量决定使用哪种 Redis 客户端
let redis: any;

if (env.UPSTASH_REDIS_REST_URL.startsWith("http://") || env.UPSTASH_REDIS_REST_URL.startsWith("redis://")) {
  // 使用本地 Redis 适配器
  redis = new LocalRedisClient();
} else {
  // 使用 Upstash Redis
  const { Redis } = await import("@upstash/redis");
  redis = new Redis({
    url: env.UPSTASH_REDIS_REST_URL,
    token: env.UPSTASH_REDIS_REST_TOKEN,
  });
}

export default redis;
