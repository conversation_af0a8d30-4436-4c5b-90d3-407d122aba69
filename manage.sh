#!/bin/bash

# AMP OpenAI API 代理服务管理脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
COMPOSE_FILE="docker-compose.simple.yml"
API_KEY="amp-proxy-demo-key-2024"
BASE_URL="http://localhost:8000"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务状态
check_status() {
    log_info "检查服务状态..."
    docker-compose -f $COMPOSE_FILE ps
}

# 查看日志
show_logs() {
    log_info "显示应用日志..."
    docker-compose -f $COMPOSE_FILE logs -f amp-proxy
}

# 查看数据库日志
show_db_logs() {
    log_info "显示数据库日志..."
    docker-compose -f $COMPOSE_FILE logs -f postgres
}

# 重启服务
restart_service() {
    log_info "重启服务..."
    docker-compose -f $COMPOSE_FILE restart
    log_success "服务重启完成"
}

# 停止服务
stop_service() {
    log_info "停止服务..."
    docker-compose -f $COMPOSE_FILE down
    log_success "服务已停止"
}

# 启动服务
start_service() {
    log_info "启动服务..."
    docker-compose -f $COMPOSE_FILE up -d
    log_success "服务已启动"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查容器状态
    if docker-compose -f $COMPOSE_FILE ps | grep -q "Up"; then
        log_success "容器运行正常"
    else
        log_error "容器未正常运行"
        return 1
    fi
    
    # 检查 API 响应
    if curl -s -f -H "Authorization: Bearer $API_KEY" $BASE_URL/ > /dev/null; then
        log_success "API 响应正常"
        
        # 显示 API 密钥状态
        log_info "API 密钥状态:"
        curl -s -H "Authorization: Bearer $API_KEY" $BASE_URL/api-keys | jq '.[0:2]' || curl -s -H "Authorization: Bearer $API_KEY" $BASE_URL/api-keys
    else
        log_error "API 无法访问"
        return 1
    fi
}

# 测试 API
test_api() {
    log_info "测试 API 功能..."
    
    echo "1. 基础健康检查:"
    curl -s -H "Authorization: Bearer $API_KEY" $BASE_URL/ | jq . || curl -s -H "Authorization: Bearer $API_KEY" $BASE_URL/
    
    echo -e "\n2. API 密钥列表:"
    curl -s -H "Authorization: Bearer $API_KEY" $BASE_URL/api-keys | jq . || curl -s -H "Authorization: Bearer $API_KEY" $BASE_URL/api-keys
    
    echo -e "\n3. 测试 OpenAI 兼容 API (需要有效的 Portkey 配置):"
    curl -X POST $BASE_URL/v1/chat/completions \
      -H "Authorization: Bearer $API_KEY" \
      -H "Content-Type: application/json" \
      -d '{
        "model": "claude-3-sonnet-20240229",
        "messages": [{"role": "user", "content": "Hello"}],
        "max_tokens": 10
      }' | jq . 2>/dev/null || echo "API 调用失败 (可能需要配置有效的 Portkey API 密钥)"
}

# 连接数据库
connect_db() {
    log_info "连接到数据库..."
    docker exec -it $(docker-compose -f $COMPOSE_FILE ps -q postgres) psql -U ampuser -d ampproxy
}

# 备份数据库
backup_db() {
    log_info "备份数据库..."
    local backup_file="backup_$(date +%Y%m%d_%H%M%S).sql"
    docker exec $(docker-compose -f $COMPOSE_FILE ps -q postgres) pg_dump -U ampuser ampproxy > $backup_file
    log_success "数据库备份完成: $backup_file"
}

# 查看配置
show_config() {
    log_info "当前配置:"
    echo "Compose 文件: $COMPOSE_FILE"
    echo "API 密钥: $API_KEY"
    echo "服务地址: $BASE_URL"
    echo ""
    echo "环境变量 (从 docker-compose 文件):"
    grep -A 20 "environment:" $COMPOSE_FILE | grep -E "^\s*-\s*[A-Z_]+" | sed 's/^[[:space:]]*-[[:space:]]*/  /'
}

# 更新配置
update_config() {
    log_info "配置更新指南:"
    echo ""
    echo "1. 编辑 $COMPOSE_FILE 文件"
    echo "2. 修改 environment 部分的环境变量"
    echo "3. 运行 './manage.sh restart' 重启服务"
    echo ""
    echo "重要配置项:"
    echo "  - PORTKEY_API_KEY: Portkey API 密钥 (必需)"
    echo "  - STEEL_API_KEY: Steel SDK 密钥 (可选)"
    echo "  - HUMKT_API_KEY: HUMKT API 密钥 (可选)"
    echo "  - API_KEY: 客户端认证密钥"
}

# 显示使用帮助
show_help() {
    echo "AMP OpenAI API 代理服务管理脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [命令]"
    echo ""
    echo "可用命令:"
    echo "  status      查看服务状态"
    echo "  start       启动服务"
    echo "  stop        停止服务"
    echo "  restart     重启服务"
    echo "  logs        查看应用日志"
    echo "  db-logs     查看数据库日志"
    echo "  health      健康检查"
    echo "  test        测试 API 功能"
    echo "  db          连接数据库"
    echo "  backup      备份数据库"
    echo "  config      查看当前配置"
    echo "  update      配置更新指南"
    echo "  help        显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 health   # 执行健康检查"
    echo "  $0 test     # 测试 API 功能"
    echo "  $0 logs     # 查看日志"
}

# 主函数
main() {
    case "${1:-help}" in
        "status")
            check_status
            ;;
        "start")
            start_service
            ;;
        "stop")
            stop_service
            ;;
        "restart")
            restart_service
            ;;
        "logs")
            show_logs
            ;;
        "db-logs")
            show_db_logs
            ;;
        "health")
            health_check
            ;;
        "test")
            test_api
            ;;
        "db")
            connect_db
            ;;
        "backup")
            backup_db
            ;;
        "config")
            show_config
            ;;
        "update")
            update_config
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
