#!/bin/bash

# AMP OpenAI API 代理服务部署脚本
# 使用方法: ./deploy.sh [start|stop|restart|logs|status]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 和 Docker Compose
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 检查环境变量文件
check_env_file() {
    log_info "检查环境变量文件..."
    
    if [ ! -f ".env" ]; then
        log_warning ".env 文件不存在，从 .env.example 复制..."
        if [ -f ".env.example" ]; then
            cp .env.example .env
            log_warning "请编辑 .env 文件，配置正确的环境变量"
            log_warning "配置完成后重新运行部署脚本"
            exit 1
        else
            log_error ".env.example 文件不存在"
            exit 1
        fi
    fi
    
    # 检查关键环境变量
    source .env
    
    if [ -z "$API_KEY" ] || [ "$API_KEY" = "your-secure-api-key-here" ]; then
        log_error "请在 .env 文件中设置正确的 API_KEY"
        exit 1
    fi
    
    if [ -z "$DATABASE_URL" ] || [[ "$DATABASE_URL" == *"username:password"* ]]; then
        log_error "请在 .env 文件中设置正确的 DATABASE_URL"
        exit 1
    fi
    
    log_success "环境变量检查通过"
}

# 构建镜像
build_image() {
    log_info "构建 Docker 镜像..."
    docker-compose build
    log_success "镜像构建完成"
}

# 启动服务
start_service() {
    log_info "启动服务..."
    docker-compose up -d
    log_success "服务启动完成"
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    check_health
}

# 停止服务
stop_service() {
    log_info "停止服务..."
    docker-compose down
    log_success "服务已停止"
}

# 重启服务
restart_service() {
    log_info "重启服务..."
    docker-compose restart
    log_success "服务重启完成"
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    check_health
}

# 查看日志
show_logs() {
    log_info "显示服务日志..."
    docker-compose logs -f
}

# 检查服务状态
check_status() {
    log_info "检查服务状态..."
    docker-compose ps
}

# 健康检查
check_health() {
    log_info "执行健康检查..."
    
    # 检查容器状态
    if docker-compose ps | grep -q "Up"; then
        log_success "容器运行正常"
    else
        log_error "容器未正常运行"
        return 1
    fi
    
    # 检查 API 响应
    if curl -s -f http://localhost:8000/ > /dev/null; then
        log_success "API 响应正常"
        log_info "服务地址: http://localhost:8000"
        log_info "API 文档: http://localhost:8000/api-keys"
    else
        log_warning "API 暂时无法访问，可能还在启动中..."
    fi
}

# 清理资源
cleanup() {
    log_info "清理 Docker 资源..."
    docker-compose down -v
    docker system prune -f
    log_success "清理完成"
}

# 显示使用帮助
show_help() {
    echo "AMP OpenAI API 代理服务部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [命令]"
    echo ""
    echo "可用命令:"
    echo "  start     启动服务"
    echo "  stop      停止服务"
    echo "  restart   重启服务"
    echo "  logs      查看日志"
    echo "  status    查看状态"
    echo "  health    健康检查"
    echo "  cleanup   清理资源"
    echo "  help      显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 start    # 启动服务"
    echo "  $0 logs     # 查看日志"
    echo "  $0 status   # 查看状态"
}

# 主函数
main() {
    case "${1:-start}" in
        "start")
            check_dependencies
            check_env_file
            build_image
            start_service
            ;;
        "stop")
            stop_service
            ;;
        "restart")
            restart_service
            ;;
        "logs")
            show_logs
            ;;
        "status")
            check_status
            ;;
        "health")
            check_health
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
